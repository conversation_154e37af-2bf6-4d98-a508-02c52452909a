<?php
// Include configuration and functions
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Set JSON header
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');

// Initialize response
$response = [
    'success' => false,
    'data' => [],
    'message' => ''
];

try {
    // Get database connection
    $conn = getDbConnection();
    
    // Get locations
    $locations = getLocations($conn);
    
    // Format response
    $response['success'] = true;
    $response['data'] = $locations;
    $response['message'] = 'Locations retrieved successfully';
    
} catch (Exception $e) {
    // Log error
    error_log('API Error (get-locations): ' . $e->getMessage());
    
    $response['success'] = false;
    $response['message'] = 'An error occurred while fetching locations';
}

// Return JSON response
echo json_encode($response, JSON_PRETTY_PRINT);
?>