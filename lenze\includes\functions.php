<?php
// Include configuration
require_once __DIR__ . '/config.php';

// Function to sanitize input
function sanitizeInput($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

// Function to validate email
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

// Function to get categories
function getCategories($conn = null) {
    if (!$conn) {
        $conn = getDbConnection();
    }
    
    // For now, return static data (will be replaced with database query)
    return [
        [
            'id' => 1,
            'name' => 'Design & Creative',
            'icon' => 'fa-palette',
            'job_count' => 1234
        ],
        [
            'id' => 2,
            'name' => 'Development & IT',
            'icon' => 'fa-code',
            'job_count' => 2156
        ],
        [
            'id' => 3,
            'name' => 'Writing & Translation',
            'icon' => 'fa-pen-nib',
            'job_count' => 987
        ],
        [
            'id' => 4,
            'name' => 'Marketing',
            'icon' => 'fa-bullhorn',
            'job_count' => 1543
        ]
    ];
}

// Function to get top freelancers
function getTopFreelancers($conn = null, $limit = 4) {
    if (!$conn) {
        $conn = getDbConnection();
    }
    
    // For now, return static data (will be replaced with database query)
    return [
        [
            'id' => 1,
            'name' => 'Sarah Johnson',
            'title' => 'UI/UX Designer',
            'avatar' => 'avatar1.jpg',
            'rating' => 5.0,
            'review_count' => 120,
            'hourly_rate' => 50,
            'is_pro' => true
        ],
        [
            'id' => 2,
            'name' => 'Michael Chen',
            'title' => 'Full Stack Developer',
            'avatar' => 'avatar2.jpg',
            'rating' => 4.9,
            'review_count' => 89,
            'hourly_rate' => 75,
            'is_pro' => true
        ],
        [
            'id' => 3,
            'name' => 'Emma Williams',
            'title' => 'Content Writer',
            'avatar' => 'avatar3.jpg',
            'rating' => 4.8,
            'review_count' => 156,
            'hourly_rate' => 35,
            'is_pro' => false
        ],
        [
            'id' => 4,
            'name' => 'David Martinez',
            'title' => 'Digital Marketer',
            'avatar' => 'avatar4.jpg',
            'rating' => 5.0,
            'review_count' => 67,
            'hourly_rate' => 45,
            'is_pro' => true
        ]
    ];
}

// Function to get locations
function getLocations($conn = null) {
    if (!$conn) {
        $conn = getDbConnection();
    }
    
    // For now, return static data
    return [
        ['id' => 1, 'city' => 'New York', 'country' => 'USA'],
        ['id' => 2, 'city' => 'London', 'country' => 'UK'],
        ['id' => 3, 'city' => 'Toronto', 'country' => 'Canada'],
        ['id' => 4, 'city' => 'Sydney', 'country' => 'Australia'],
        ['id' => 5, 'city' => 'Berlin', 'country' => 'Germany'],
        ['id' => 6, 'city' => 'Tokyo', 'country' => 'Japan'],
        ['id' => 7, 'city' => 'Mumbai', 'country' => 'India'],
        ['id' => 8, 'city' => 'Cape Town', 'country' => 'South Africa']
    ];
}

// Function to subscribe to newsletter
function subscribeNewsletter($email, $conn = null) {
    if (!$conn) {
        $conn = getDbConnection();
    }
    
    // Validate email
    if (!validateEmail($email)) {
        return ['success' => false, 'message' => 'Please enter a valid email address.'];
    }
    
    try {
        // Check if email already exists
        $stmt = $conn->prepare("SELECT id FROM newsletter WHERE email = ?");
        $stmt->execute([$email]);
        
        if ($stmt->rowCount() > 0) {
            return ['success' => false, 'message' => 'This email is already subscribed.'];
        }
        
        // Insert new subscriber
        $stmt = $conn->prepare("INSERT INTO newsletter (email) VALUES (?)");
        $stmt->execute([$email]);
        
        return ['success' => true, 'message' => 'Successfully subscribed to our newsletter!'];
    } catch(PDOException $e) {
        return ['success' => false, 'message' => 'An error occurred. Please try again.'];
    }
}

// Function to get blog posts
function getBlogPosts($limit = 3) {
    // Static data for demo
    return [
        [
            'id' => 1,
            'title' => '10 Tips for Successful Freelancing',
            'excerpt' => 'Discover the secrets to building a thriving freelance career with these essential tips.',
            'image' => 'blog1.jpg',
            'date' => '2024-01-15',
            'author' => 'John Doe',
            'category' => 'Freelancing Tips'
        ],
        [
            'id' => 2,
            'title' => 'How to Set Your Freelance Rates',
            'excerpt' => 'Learn how to price your services competitively while ensuring fair compensation.',
            'image' => 'blog2.jpg',
            'date' => '2024-01-10',
            'author' => 'Jane Smith',
            'category' => 'Business'
        ],
        [
            'id' => 3,
            'title' => 'Remote Work Best Practices',
            'excerpt' => 'Master the art of working from home with these productivity-boosting strategies.',
            'image' => 'blog3.jpg',
            'date' => '2024-01-05',
            'author' => 'Mike Johnson',
            'category' => 'Productivity'
        ]
    ];
}

// Function to format date
function formatDate($date) {
    return date('F d, Y', strtotime($date));
}

// Function to generate star rating HTML
function generateStarRating($rating) {
    $fullStars = floor($rating);
    $halfStar = ($rating - $fullStars) >= 0.5;
    $emptyStars = 5 - $fullStars - ($halfStar ? 1 : 0);
    
    $html = '<span class="stars">';
    
    // Full stars
    for ($i = 0; $i < $fullStars; $i++) {
        $html .= '<i class="fas fa-star"></i>';
    }
    
    // Half star
    if ($halfStar) {
        $html .= '<i class="fas fa-star-half-alt"></i>';
    }
    
    // Empty stars
    for ($i = 0; $i < $emptyStars; $i++) {
        $html .= '<i class="far fa-star"></i>';
    }
    
    $html .= '</span>';
    
    return $html;
}

// Function to get FAQs
function getFAQs() {
    return [
        [
            'question' => 'How do I get started as a freelancer?',
            'answer' => 'Getting started is easy! Simply sign up for a free account, complete your profile with your skills and experience, and start browsing available jobs. You can apply to projects that match your expertise.'
        ],
        [
            'question' => 'Is there a fee to join Lenze?',
            'answer' => 'Joining Lenze is completely free for both freelancers and clients. We only charge a small service fee when you successfully complete a project.'
        ],
        [
            'question' => 'How do payments work?',
            'answer' => 'We use a secure escrow system. Clients deposit funds when hiring a freelancer, and payments are released to freelancers upon project completion and client approval.'
        ],
        [
            'question' => 'What if I have a dispute with a client/freelancer?',
            'answer' => 'Our dedicated support team is here to help resolve any disputes. We offer mediation services and have clear policies in place to protect both parties.'
        ],
        [
            'question' => 'Can I work with international clients?',
            'answer' => 'Absolutely! Lenze connects freelancers and clients from around the world. We support multiple currencies and payment methods for international transactions.'
        ],
        [
            'question' => 'How do I ensure my profile stands out?',
            'answer' => 'Complete your profile 100%, showcase your best work in your portfolio, get client reviews, and keep your skills updated. Professional photos and detailed descriptions also help.'
        ]
    ];
}
?>