<?php
// Database setup script
require_once 'includes/config.php';

echo "Setting up Lenze database...\n\n";

try {
    // Create connection without database
    $conn = new PDO("mysql:host=" . DB_HOST, DB_USER, DB_PASS);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Create database if not exists
    $conn->exec("CREATE DATABASE IF NOT EXISTS " . DB_NAME);
    echo "✓ Database created/verified: " . DB_NAME . "\n";
    
    // Use the database
    $conn->exec("USE " . DB_NAME);
    
    // Create categories table
    $conn->exec("
        CREATE TABLE IF NOT EXISTS categories (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(100),
            icon VARCHAR(50),
            job_count INT DEFAULT 0
        )
    ");
    echo "✓ Categories table created\n";
    
    // Create newsletter table
    $conn->exec("
        CREATE TABLE IF NOT EXISTS newsletter (
            id INT PRIMARY KEY AUTO_INCREMENT,
            email VARCHAR(255) UNIQUE,
            subscribed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ");
    echo "✓ Newsletter table created\n";
    
    // Create locations table
    $conn->exec("
        CREATE TABLE IF NOT EXISTS locations (
            id INT PRIMARY KEY AUTO_INCREMENT,
            city VARCHAR(100),
            country VARCHAR(100)
        )
    ");
    echo "✓ Locations table created\n";
    
    // Create freelancers table
    $conn->exec("
        CREATE TABLE IF NOT EXISTS freelancers (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(100),
            title VARCHAR(100),
            avatar VARCHAR(255),
            rating DECIMAL(2,1),
            review_count INT,
            hourly_rate DECIMAL(6,2),
            is_pro BOOLEAN DEFAULT FALSE
        )
    ");
    echo "✓ Freelancers table created\n";
    
    // Create contact messages table
    $conn->exec("
        CREATE TABLE IF NOT EXISTS contact_messages (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(100),
            email VARCHAR(255),
            phone VARCHAR(50),
            subject VARCHAR(200),
            message TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ");
    echo "✓ Contact messages table created\n";
    
    // Insert sample data for categories
    $categories = [
        ['Design & Creative', 'fa-palette', 1234],
        ['Development & IT', 'fa-code', 2156],
        ['Writing & Translation', 'fa-pen-nib', 987],
        ['Marketing', 'fa-bullhorn', 1543]
    ];
    
    $stmt = $conn->prepare("INSERT IGNORE INTO categories (name, icon, job_count) VALUES (?, ?, ?)");
    foreach ($categories as $cat) {
        $stmt->execute($cat);
    }
    echo "✓ Sample categories inserted\n";
    
    // Insert sample locations
    $locations = [
        ['New York', 'USA'],
        ['London', 'UK'],
        ['Toronto', 'Canada'],
        ['Sydney', 'Australia'],
        ['Berlin', 'Germany'],
        ['Tokyo', 'Japan'],
        ['Mumbai', 'India'],
        ['Cape Town', 'South Africa']
    ];
    
    $stmt = $conn->prepare("INSERT IGNORE INTO locations (city, country) VALUES (?, ?)");
    foreach ($locations as $loc) {
        $stmt->execute($loc);
    }
    echo "✓ Sample locations inserted\n";
    
    // Insert sample freelancers
    $freelancers = [
        ['Sarah Johnson', 'UI/UX Designer', 'avatar1.jpg', 5.0, 120, 50.00, 1],
        ['Michael Chen', 'Full Stack Developer', 'avatar2.jpg', 4.9, 89, 75.00, 1],
        ['Emma Williams', 'Content Writer', 'avatar3.jpg', 4.8, 156, 35.00, 0],
        ['David Martinez', 'Digital Marketer', 'avatar4.jpg', 5.0, 67, 45.00, 1]
    ];
    
    $stmt = $conn->prepare("INSERT IGNORE INTO freelancers (name, title, avatar, rating, review_count, hourly_rate, is_pro) VALUES (?, ?, ?, ?, ?, ?, ?)");
    foreach ($freelancers as $freelancer) {
        $stmt->execute($freelancer);
    }
    echo "✓ Sample freelancers inserted\n";
    
    echo "\n✅ Database setup completed successfully!\n";
    echo "\nYou can now access the site at: http://localhost/lenze/\n";
    
} catch(PDOException $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>