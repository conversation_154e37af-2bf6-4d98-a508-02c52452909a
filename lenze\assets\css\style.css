/* ===================================
   Lenze - Freelance Platform CSS
   =================================== */

/* CSS Variables */
:root {
    /* Primary Colors */
    --primary-green: #00C853;
    --primary-green-hover: #00A041;
    --dark-green: #00897B;
    
    /* Text Colors */
    --text-primary: #2C3E50;
    --text-secondary: #7F8C8D;
    --text-white: #FFFFFF;
    
    /* Background Colors */
    --bg-white: #FFFFFF;
    --bg-light-gray: #F5F5F5;
    --bg-dark: #1A1A1A;
    --bg-footer: #232323;
    
    /* Additional Colors */
    --border-light: #E0E0E0;
    --shadow: rgba(0, 0, 0, 0.1);
    
    /* Spacing */
    --section-padding: 80px 0;
    --section-padding-mobile: 40px 0;
    --container-max-width: 1200px;
    --container-padding: 0 15px;
    --grid-gap-large: 30px;
    --grid-gap-medium: 20px;
    --grid-gap-small: 15px;
    --card-padding: 30px;
    --card-padding-mobile: 20px;
}

/* Reset & Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, sans-serif;
    font-size: 16px;
    line-height: 1.5;
    color: var(--text-primary);
    background-color: var(--bg-white);
}

h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    color: var(--text-primary);
}

h1 {
    font-size: 48px;
    font-weight: 700;
    line-height: 56px;
}

h2 {
    font-size: 36px;
    font-weight: 700;
    line-height: 44px;
}

h3 {
    font-size: 28px;
    line-height: 36px;
}

h4 {
    font-size: 20px;
    line-height: 28px;
}

h5 {
    font-size: 16px;
    line-height: 24px;
}

a {
    text-decoration: none;
    color: inherit;
    transition: all 0.3s ease;
}

img {
    max-width: 100%;
    height: auto;
}

/* Utility Classes */
.container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: var(--container-padding);
}

.text-primary {
    color: var(--primary-green);
}

.text-white {
    color: var(--text-white);
}

.text-center {
    text-align: center;
}

.mt-4 {
    margin-top: 2rem;
}

/* Section Styles */
section {
    padding: var(--section-padding);
}

.section-header {
    text-align: center;
    margin-bottom: 50px;
}

.section-header h2 {
    margin-bottom: 15px;
}

.section-header p {
    font-size: 18px;
    color: var(--text-secondary);
}

/* Button Styles */
.btn {
    display: inline-block;
    padding: 12px 30px;
    border-radius: 5px;
    font-weight: 600;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
    font-size: 16px;
}

.btn-primary {
    background-color: var(--primary-green);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-green-hover);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 200, 83, 0.3);
}

.btn-outline {
    background-color: transparent;
    color: var(--text-primary);
    border: 2px solid var(--border-light);
}

.btn-outline:hover {
    border-color: var(--primary-green);
    color: var(--primary-green);
}

.btn-white {
    background-color: white;
    color: var(--primary-green);
}

.btn-white:hover {
    background-color: #f8f8f8;
    transform: translateY(-2px);
}

.btn-sm {
    padding: 8px 20px;
    font-size: 14px;
}

/* Navigation Styles */
.navbar {
    background-color: white;
    box-shadow: 0 2px 10px var(--shadow);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
}

.nav-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 0;
}

.nav-brand .logo {
    font-size: 32px;
    font-weight: 700;
    color: var(--primary-green);
}

.nav-menu {
    display: flex;
    list-style: none;
    align-items: center;
    gap: 30px;
}

.nav-link {
    color: var(--text-primary);
    font-weight: 500;
    transition: color 0.3s;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-green);
}

.nav-auth {
    display: flex;
    gap: 15px;
    align-items: center;
}

.mobile-toggle {
    display: none;
    flex-direction: column;
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
}

.mobile-toggle span {
    width: 25px;
    height: 3px;
    background-color: var(--text-primary);
    margin: 3px 0;
    transition: 0.3s;
}

.mobile-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
}

/* Hero Section */
.hero {
    background-color: var(--primary-green);
    padding-top: 120px;
    color: white;
}

.hero-wrapper {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 50px;
    align-items: center;
}

.hero-content h1 {
    color: white;
    margin-bottom: 20px;
}

.hero-subtitle {
    font-size: 20px;
    margin-bottom: 30px;
    opacity: 0.9;
}

.search-form {
    margin-bottom: 30px;
}

.search-inputs {
    display: flex;
    gap: 10px;
    background-color: white;
    padding: 10px;
    border-radius: 10px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.search-select,
.search-input {
    flex: 1;
    padding: 12px 15px;
    border: 1px solid var(--border-light);
    border-radius: 5px;
    font-size: 14px;
    color: var(--text-primary);
    background-color: white;
}

.search-select:focus,
.search-input:focus {
    outline: none;
    border-color: var(--primary-green);
}

.btn-search {
    flex-shrink: 0;
}

.hero-stats {
    display: flex;
    gap: 30px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 14px;
    opacity: 0.9;
}

.stat-item i {
    font-size: 18px;
}

.hero-image img {
    width: 100%;
    height: auto;
}

/* Categories Section */
.categories {
    background-color: var(--bg-light-gray);
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--grid-gap-large);
}

.category-card {
    background-color: white;
    padding: var(--card-padding);
    border-radius: 10px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px var(--shadow);
}

.category-icon {
    width: 80px;
    height: 80px;
    background-color: rgba(0, 200, 83, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
}

.category-icon i {
    font-size: 36px;
    color: var(--primary-green);
}

.category-card h4 {
    margin-bottom: 10px;
}

.category-card p {
    color: var(--text-secondary);
}

/* Statistics Section */
.statistics {
    background-color: white;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--grid-gap-large);
}

.stat-box {
    text-align: center;
    padding: var(--card-padding);
}

.stat-icon {
    width: 70px;
    height: 70px;
    background-color: rgba(0, 200, 83, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
}

.stat-icon i {
    font-size: 30px;
    color: var(--primary-green);
}

.stat-box h3 {
    font-size: 36px;
    color: var(--primary-green);
    margin-bottom: 10px;
}

/* Dual CTA Section */
.dual-cta {
    background-color: var(--bg-light-gray);
}

.cta-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--grid-gap-large);
}

.cta-box {
    background-color: white;
    border-radius: 10px;
    padding: 50px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 30px;
}

.freelancer-cta {
    background-color: var(--primary-green);
    color: white;
}

.freelancer-cta h3,
.freelancer-cta p {
    color: white;
}

.cta-content h3 {
    margin-bottom: 15px;
}

.cta-content p {
    margin-bottom: 25px;
}

.cta-features {
    list-style: none;
    margin-bottom: 25px;
}

.cta-features li {
    padding: 8px 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.cta-features i {
    color: var(--primary-green);
}

.freelancer-cta .cta-features i {
    color: white;
}

.cta-image img {
    width: 200px;
}

/* Features Section */
.features {
    background-color: white;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--grid-gap-large);
}

.feature-card {
    text-align: center;
    padding: var(--card-padding);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background-color: rgba(0, 200, 83, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
}

.feature-icon i {
    font-size: 36px;
    color: var(--primary-green);
}

.feature-card h4 {
    margin-bottom: 15px;
}

.feature-card p {
    color: var(--text-secondary);
}

/* Secondary Hero Section */
.secondary-hero {
    background-color: var(--primary-green);
    color: white;
}

.secondary-hero-wrapper {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 50px;
    align-items: center;
}

.secondary-hero-content h2 {
    color: white;
    margin-bottom: 20px;
}

.secondary-hero-content p {
    font-size: 18px;
    margin-bottom: 30px;
    opacity: 0.9;
}

.secondary-hero-image img {
    width: 100%;
}

/* How It Works Section */
.how-it-works {
    background-color: var(--bg-light-gray);
}

.process-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
}

.process-column h3 {
    margin-bottom: 30px;
    color: var(--primary-green);
}

.process-steps {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.process-step {
    display: flex;
    gap: 20px;
    align-items: flex-start;
}

.step-number {
    width: 40px;
    height: 40px;
    background-color: var(--primary-green);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    flex-shrink: 0;
}

.step-content h4 {
    margin-bottom: 8px;
}

.step-content p {
    color: var(--text-secondary);
}

/* Portfolio Section */
.portfolio {
    background-color: white;
}

.portfolio-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--grid-gap-medium);
}

.portfolio-item {
    position: relative;
    overflow: hidden;
    border-radius: 10px;
    cursor: pointer;
}

.portfolio-item img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.portfolio-item:hover img {
    transform: scale(1.1);
}

.portfolio-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
    color: white;
    padding: 20px;
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.portfolio-item:hover .portfolio-overlay {
    transform: translateY(0);
}

.portfolio-overlay h4 {
    color: white;
    margin-bottom: 5px;
}

.portfolio-overlay p {
    font-size: 14px;
    opacity: 0.9;
}

/* Testimonials Section */
.testimonials {
    background-color: var(--bg-dark);
    color: white;
}

.testimonials .section-header h2,
.testimonials .section-header p {
    color: white;
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--grid-gap-large);
}

.testimonial-card {
    background-color: rgba(255, 255, 255, 0.05);
    padding: var(--card-padding);
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.quote-icon {
    font-size: 30px;
    color: var(--primary-green);
    margin-bottom: 20px;
}

.testimonial-content p {
    font-style: italic;
    margin-bottom: 20px;
    line-height: 1.6;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 15px;
}

.testimonial-author img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
}

.author-info h5 {
    color: white;
    margin-bottom: 3px;
}

.author-info span {
    font-size: 14px;
    color: var(--primary-green);
}

/* Top Freelancers Section */
.top-freelancers {
    background-color: var(--bg-light-gray);
}

.freelancers-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--grid-gap-large);
}

.freelancer-card {
    background-color: white;
    padding: var(--card-padding);
    border-radius: 10px;
    text-align: center;
    transition: all 0.3s ease;
}

.freelancer-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px var(--shadow);
}

.freelancer-avatar {
    position: relative;
    width: 100px;
    height: 100px;
    margin: 0 auto 20px;
}

.freelancer-avatar img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
}

.freelancer-avatar .badge {
    position: absolute;
    bottom: 5px;
    right: 5px;
    background-color: var(--primary-green);
    color: white;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 12px;
    font-weight: 600;
}

.freelancer-card h5 {
    margin-bottom: 5px;
}

.freelancer-card .title {
    color: var(--text-secondary);
    margin-bottom: 10px;
}

.rating {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-bottom: 10px;
}

.stars {
    color: #FFB800;
}

.rating span:last-child {
    color: var(--text-secondary);
    font-size: 14px;
}

.freelancer-card .rate {
    font-size: 20px;
    font-weight: 600;
    color: var(--primary-green);
    margin-bottom: 15px;
}

/* Newsletter Section */
.newsletter {
    background-color: white;
    position: relative;
    overflow: hidden;
}

.newsletter-wrapper {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 50px;
    align-items: center;
}

.newsletter-content h2 {
    margin-bottom: 15px;
}

.newsletter-content p {
    color: var(--text-secondary);
    margin-bottom: 30px;
}

.newsletter-form {
    max-width: 500px;
}

.newsletter-form .form-group {
    display: flex;
    gap: 10px;
}

.form-control {
    flex: 1;
    padding: 15px 20px;
    border: 1px solid var(--border-light);
    border-radius: 5px;
    font-size: 16px;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-green);
}

.newsletter-message {
    margin-top: 15px;
    padding: 10px;
    border-radius: 5px;
    display: none;
}

.newsletter-message.success {
    background-color: rgba(0, 200, 83, 0.1);
    color: var(--primary-green);
    display: block;
}

.newsletter-message.error {
    background-color: rgba(231, 76, 60, 0.1);
    color: #e74c3c;
    display: block;
}

.newsletter-note {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 15px;
    font-size: 14px;
    color: var(--text-secondary);
}

.newsletter-decoration {
    position: relative;
}

.circle-bg {
    width: 300px;
    height: 300px;
    background-color: var(--primary-green);
    border-radius: 50%;
    opacity: 0.1;
}

/* FAQ Section */
.faq {
    background-color: var(--bg-light-gray);
}

.faq-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--grid-gap-large);
}

.faq-item {
    background-color: white;
    border-radius: 10px;
    margin-bottom: 15px;
    overflow: hidden;
}

.faq-question {
    padding: 20px 25px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background-color 0.3s;
}

.faq-question:hover {
    background-color: var(--bg-light-gray);
}

.faq-question h4 {
    font-size: 16px;
    font-weight: 500;
}

.faq-question i {
    transition: transform 0.3s;
    color: var(--primary-green);
}

.faq-item.active .faq-question i {
    transform: rotate(180deg);
}

.faq-answer {
    padding: 0 25px;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease, padding 0.3s ease;
}

.faq-item.active .faq-answer {
    padding: 0 25px 20px;
    max-height: 200px;
}

.faq-answer p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Blog Section */
.blog {
    background-color: white;
}

.blog-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--grid-gap-large);
}

.blog-card {
    background-color: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px var(--shadow);
    transition: all 0.3s ease;
}

.blog-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px var(--shadow);
}

.blog-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.blog-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.blog-category {
    position: absolute;
    top: 15px;
    left: 15px;
    background-color: var(--primary-green);
    color: white;
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
}

.blog-content {
    padding: 25px;
}

.blog-meta {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
    font-size: 14px;
    color: var(--text-secondary);
}

.blog-meta i {
    margin-right: 5px;
}

.blog-content h4 {
    margin-bottom: 10px;
}

.blog-content h4 a {
    color: var(--text-primary);
}

.blog-content h4 a:hover {
    color: var(--primary-green);
}

.blog-content p {
    color: var(--text-secondary);
    margin-bottom: 15px;
    line-height: 1.6;
}

.read-more {
    color: var(--primary-green);
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.read-more:hover {
    gap: 10px;
}

/* Bottom CTA Section */
.bottom-cta {
    background-color: var(--bg-light-gray);
    padding: 60px 0;
}

.cta-boxes-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--grid-gap-large);
}

.cta-box-item {
    background-color: var(--primary-green);
    color: white;
    padding: 40px;
    border-radius: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.cta-box-item:last-child {
    background-color: var(--bg-dark);
}

.cta-box-content h3 {
    color: white;
    margin-bottom: 10px;
}

.cta-box-content p {
    margin-bottom: 20px;
    opacity: 0.9;
}

.cta-box-icon {
    font-size: 60px;
    opacity: 0.3;
}

/* Footer Styles */
.footer {
    background-color: var(--bg-footer);
    color: white;
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
    gap: var(--grid-gap-large);
    margin-bottom: 40px;
}

.footer-logo {
    font-size: 28px;
    font-weight: 700;
    color: var(--primary-green);
    margin-bottom: 15px;
}

.footer-desc {
    color: #ccc;
    line-height: 1.6;
    margin-bottom: 20px;
}

.footer-social {
    display: flex;
    gap: 15px;
}

.social-link {
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s;
}

.social-link:hover {
    background-color: var(--primary-green);
}

.footer-column h4 {
    color: white;
    margin-bottom: 20px;
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: 10px;
}

.footer-links a {
    color: #ccc;
    transition: color 0.3s;
}

.footer-links a:hover {
    color: var(--primary-green);
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 20px;
}

.footer-bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.footer-bottom p {
    color: #ccc;
}

.footer-bottom-links {
    display: flex;
    gap: 20px;
}

.footer-bottom-links a {
    color: #ccc;
    font-size: 14px;
}

.footer-bottom-links a:hover {
    color: var(--primary-green);
}