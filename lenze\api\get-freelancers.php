<?php
// Include configuration and functions
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Set JSON header
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');

// Initialize response
$response = [
    'success' => false,
    'data' => [],
    'message' => '',
    'pagination' => []
];

try {
    // Get database connection
    $conn = getDbConnection();
    
    // Get query parameters
    $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
    $limit = isset($_GET['limit']) ? min(50, max(1, intval($_GET['limit']))) : 10;
    $category = isset($_GET['category']) ? sanitizeInput($_GET['category']) : '';
    $location = isset($_GET['location']) ? sanitizeInput($_GET['location']) : '';
    $rating = isset($_GET['min_rating']) ? floatval($_GET['min_rating']) : 0;
    $sort = isset($_GET['sort']) ? sanitizeInput($_GET['sort']) : 'rating_desc';
    
    // Calculate offset
    $offset = ($page - 1) * $limit;
    
    // Get freelancers (using static data for now)
    $allFreelancers = getTopFreelancers($conn, 20);
    
    // Apply filters (in real app, this would be done in SQL)
    $filteredFreelancers = array_filter($allFreelancers, function($freelancer) use ($rating) {
        return $freelancer['rating'] >= $rating;
    });
    
    // Sort freelancers
    switch ($sort) {
        case 'rating_asc':
            usort($filteredFreelancers, function($a, $b) {
                return $a['rating'] <=> $b['rating'];
            });
            break;
        case 'rating_desc':
            usort($filteredFreelancers, function($a, $b) {
                return $b['rating'] <=> $a['rating'];
            });
            break;
        case 'rate_asc':
            usort($filteredFreelancers, function($a, $b) {
                return $a['hourly_rate'] <=> $b['hourly_rate'];
            });
            break;
        case 'rate_desc':
            usort($filteredFreelancers, function($a, $b) {
                return $b['hourly_rate'] <=> $a['hourly_rate'];
            });
            break;
    }
    
    // Paginate results
    $total = count($filteredFreelancers);
    $totalPages = ceil($total / $limit);
    $paginatedFreelancers = array_slice($filteredFreelancers, $offset, $limit);
    
    // Format response
    $response['success'] = true;
    $response['data'] = array_values($paginatedFreelancers);
    $response['message'] = 'Freelancers retrieved successfully';
    $response['pagination'] = [
        'current_page' => $page,
        'total_pages' => $totalPages,
        'per_page' => $limit,
        'total_items' => $total,
        'has_next' => $page < $totalPages,
        'has_prev' => $page > 1
    ];
    
} catch (Exception $e) {
    // Log error
    error_log('API Error (get-freelancers): ' . $e->getMessage());
    
    $response['success'] = false;
    $response['message'] = 'An error occurred while fetching freelancers';
}

// Return JSON response
echo json_encode($response, JSON_PRETTY_PRINT);
?>