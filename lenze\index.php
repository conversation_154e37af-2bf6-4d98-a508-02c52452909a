<?php
// Include configuration and functions
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Set page title
$pageTitle = 'Find the Best Freelance Jobs';

// Get data
$categories = getCategories();
$topFreelancers = getTopFreelancers();
$locations = getLocations();
$blogPosts = getBlogPosts();
$faqs = getFAQs();

// Include header
include 'includes/header.php';
?>

<!-- Hero Section -->
<section class="hero">
    <div class="container">
        <div class="hero-wrapper">
            <div class="hero-content">
                <h1>Find the Best<br><span class="text-primary">Freelance Jobs</span></h1>
                <p class="hero-subtitle">Work with talented people at the most affordable price</p>
                
                <!-- Search Form -->
                <form class="search-form" action="forms/search-handler.php" method="GET">
                    <div class="search-inputs">
                        <select name="location" class="search-select">
                            <option value="">Select Location</option>
                            <?php foreach($locations as $location): ?>
                                <option value="<?php echo $location['id']; ?>">
                                    <?php echo $location['city'] . ', ' . $location['country']; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        
                        <select name="category" class="search-select">
                            <option value="">Select Category</option>
                            <?php foreach($categories as $category): ?>
                                <option value="<?php echo $category['id']; ?>">
                                    <?php echo $category['name']; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        
                        <input type="text" name="keyword" class="search-input" placeholder="Try 'Web Developer'">
                        
                        <button type="submit" class="btn btn-primary btn-search">
                            <i class="fas fa-search"></i> Search
                        </button>
                    </div>
                </form>
                
                <!-- Hero Stats -->
                <div class="hero-stats">
                    <div class="stat-item">
                        <i class="fas fa-briefcase"></i>
                        <span>12k+ Jobs</span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-users"></i>
                        <span>10k+ Freelancers</span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-check-circle"></i>
                        <span>20k+ Successful Projects</span>
                    </div>
                </div>
            </div>
            
            <div class="hero-image">
                <img src="assets/images/placeholder.php?w=500&h=400&text=Hero+Illustration" alt="Freelancer working">
            </div>
        </div>
    </div>
</section>

<!-- Job Categories Section -->
<section class="categories">
    <div class="container">
        <div class="section-header">
            <h2>Browse Job Categories</h2>
            <p>Find work in your area of expertise</p>
        </div>
        
        <div class="categories-grid">
            <?php foreach($categories as $category): ?>
            <div class="category-card">
                <div class="category-icon">
                    <i class="fas <?php echo $category['icon']; ?>"></i>
                </div>
                <h4><?php echo $category['name']; ?></h4>
                <p><?php echo number_format($category['job_count']); ?> Jobs</p>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- Statistics Section -->
<section class="statistics">
    <div class="container">
        <div class="section-header">
            <h2>It's Easy to Get Work Done</h2>
            <p>Choose from millions of skilled freelancers from all over the world</p>
        </div>
        
        <div class="stats-grid">
            <div class="stat-box">
                <div class="stat-icon">
                    <i class="fas fa-trophy"></i>
                </div>
                <h3>4.9/5</h3>
                <p>Average Rating</p>
            </div>
            
            <div class="stat-box">
                <div class="stat-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <h3>24/7</h3>
                <p>Support Available</p>
            </div>
            
            <div class="stat-box">
                <div class="stat-icon">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <h3>100%</h3>
                <p>Secure Payments</p>
            </div>
            
            <div class="stat-box">
                <div class="stat-icon">
                    <i class="fas fa-users"></i>
                </div>
                <h3>2M+</h3>
                <p>Active Users</p>
            </div>
        </div>
    </div>
</section>

<!-- Dual CTA Section -->
<section class="dual-cta">
    <div class="container">
        <div class="cta-grid">
            <!-- Freelancer CTA -->
            <div class="cta-box freelancer-cta">
                <div class="cta-content">
                    <h3>Sign Up as a Freelancer</h3>
                    <p>Join our community of talented professionals and find your next opportunity</p>
                    <ul class="cta-features">
                        <li><i class="fas fa-check"></i> Set your own rates</li>
                        <li><i class="fas fa-check"></i> Work from anywhere</li>
                        <li><i class="fas fa-check"></i> Choose your projects</li>
                    </ul>
                    <a href="#" class="btn btn-white">Get Started</a>
                </div>
                <div class="cta-image">
                    <img src="assets/images/placeholder.php?w=200&h=200&text=Freelancer" alt="Freelancer">
                </div>
            </div>
            
            <!-- Client CTA -->
            <div class="cta-box client-cta">
                <div class="cta-content">
                    <h3>Sign Up as a Client</h3>
                    <p>Find skilled freelancers for your projects and get work done efficiently</p>
                    <ul class="cta-features">
                        <li><i class="fas fa-check"></i> Post jobs for free</li>
                        <li><i class="fas fa-check"></i> Access top talent</li>
                        <li><i class="fas fa-check"></i> Secure payments</li>
                    </ul>
                    <a href="#" class="btn btn-primary">Hire Talent</a>
                </div>
                <div class="cta-image">
                    <img src="assets/images/placeholder.php?w=200&h=200&text=Client" alt="Client">
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="features">
    <div class="container">
        <div class="section-header">
            <h2>Why You Should Choose Us</h2>
            <p>We provide the best platform for freelancers and clients to work together</p>
        </div>
        
        <div class="features-grid">
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <h4>Secure Payment</h4>
                <p>Your payments are protected with our secure escrow system until work is completed.</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-headset"></i>
                </div>
                <h4>24/7 Support</h4>
                <p>Our dedicated support team is available round the clock to help you with any issues.</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-users"></i>
                </div>
                <h4>Large Community</h4>
                <p>Join millions of freelancers and clients from around the world on our platform.</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-award"></i>
                </div>
                <h4>Quality Work</h4>
                <p>We ensure high-quality deliverables with our rating system and verified profiles.</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-file-alt"></i>
                </div>
                <h4>Easy Process</h4>
                <p>Simple and intuitive interface makes it easy to post jobs and submit proposals.</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <h4>Best Rates</h4>
                <p>Competitive pricing with transparent fees and no hidden charges for all users.</p>
            </div>
        </div>
    </div>
</section>

<!-- Secondary Hero Section -->
<section class="secondary-hero">
    <div class="container">
        <div class="secondary-hero-wrapper">
            <div class="secondary-hero-content">
                <h2>Find your work at home skill</h2>
                <p>Discover opportunities that match your expertise and work from the comfort of your home</p>
                <a href="#" class="btn btn-primary">Explore Opportunities</a>
            </div>
            <div class="secondary-hero-image">
                <img src="assets/images/placeholder.php?w=500&h=400&text=Woman+with+Laptop" alt="Woman with laptop">
            </div>
        </div>
    </div>
</section>

<!-- How It Works Section -->
<section class="how-it-works" id="how-it-works">
    <div class="container">
        <div class="section-header">
            <h2>How Lenze is Different</h2>
            <p>Our simple process makes it easy to connect and collaborate</p>
        </div>
        
        <div class="process-grid">
            <div class="process-column">
                <h3>For Clients</h3>
                <div class="process-steps">
                    <div class="process-step">
                        <div class="step-number">1</div>
                        <div class="step-content">
                            <h4>Post Your Job</h4>
                            <p>Describe your project and the skills required. It's free to post.</p>
                        </div>
                    </div>
                    
                    <div class="process-step">
                        <div class="step-number">2</div>
                        <div class="step-content">
                            <h4>Review Proposals</h4>
                            <p>Receive proposals from skilled freelancers within minutes.</p>
                        </div>
                    </div>
                    
                    <div class="process-step">
                        <div class="step-number">3</div>
                        <div class="step-content">
                            <h4>Hire & Collaborate</h4>
                            <p>Choose the best freelancer and start working on your project.</p>
                        </div>
                    </div>
                    
                    <div class="process-step">
                        <div class="step-number">4</div>
                        <div class="step-content">
                            <h4>Pay Securely</h4>
                            <p>Only release payment when you're 100% satisfied with the work.</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="process-column">
                <h3>For Freelancers</h3>
                <div class="process-steps">
                    <div class="process-step">
                        <div class="step-number">1</div>
                        <div class="step-content">
                            <h4>Create Profile</h4>
                            <p>Showcase your skills, experience, and portfolio to attract clients.</p>
                        </div>
                    </div>
                    
                    <div class="process-step">
                        <div class="step-number">2</div>
                        <div class="step-content">
                            <h4>Browse Jobs</h4>
                            <p>Find projects that match your skills and interests.</p>
                        </div>
                    </div>
                    
                    <div class="process-step">
                        <div class="step-number">3</div>
                        <div class="step-content">
                            <h4>Submit Proposals</h4>
                            <p>Send compelling proposals with your best price and timeline.</p>
                        </div>
                    </div>
                    
                    <div class="process-step">
                        <div class="step-number">4</div>
                        <div class="step-content">
                            <h4>Get Paid</h4>
                            <p>Complete the work and receive secure payment directly.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Portfolio Gallery Section -->
<section class="portfolio">
    <div class="container">
        <div class="section-header">
            <h2>Finishing Work Has Never Been More Creative</h2>
            <p>See examples of amazing work done by our freelancers</p>
        </div>
        
        <div class="portfolio-grid">
            <div class="portfolio-item">
                <img src="assets/images/placeholder.php?w=400&h=250&text=Mobile+App" alt="Portfolio 1">
                <div class="portfolio-overlay">
                    <h4>Mobile App Design</h4>
                    <p>UI/UX Design</p>
                </div>
            </div>
            
            <div class="portfolio-item">
                <img src="assets/images/placeholder.php?w=400&h=250&text=Brand+Identity" alt="Portfolio 2">
                <div class="portfolio-overlay">
                    <h4>Brand Identity</h4>
                    <p>Logo Design</p>
                </div>
            </div>
            
            <div class="portfolio-item">
                <img src="assets/images/placeholder.php?w=400&h=250&text=E-commerce" alt="Portfolio 3">
                <div class="portfolio-overlay">
                    <h4>E-commerce Website</h4>
                    <p>Web Development</p>
                </div>
            </div>
            
            <div class="portfolio-item">
                <img src="assets/images/placeholder.php?w=400&h=250&text=Marketing" alt="Portfolio 4">
                <div class="portfolio-overlay">
                    <h4>Marketing Campaign</h4>
                    <p>Digital Marketing</p>
                </div>
            </div>
            
            <div class="portfolio-item">
                <img src="assets/images/placeholder.php?w=400&h=250&text=Photography" alt="Portfolio 5">
                <div class="portfolio-overlay">
                    <h4>Product Photography</h4>
                    <p>Photography</p>
                </div>
            </div>
            
            <div class="portfolio-item">
                <img src="assets/images/placeholder.php?w=400&h=250&text=Content" alt="Portfolio 6">
                <div class="portfolio-overlay">
                    <h4>Content Writing</h4>
                    <p>Copywriting</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- User Feedback Section -->
<section class="testimonials">
    <div class="container">
        <div class="section-header text-white">
            <h2>Our Users Feedback</h2>
            <p>What our clients and freelancers say about us</p>
        </div>
        
        <div class="testimonials-grid">
            <div class="testimonial-card">
                <div class="testimonial-content">
                    <div class="quote-icon">
                        <i class="fas fa-quote-left"></i>
                    </div>
                    <p>"Lenze has transformed how I work. I've found amazing clients and built lasting relationships. The platform is intuitive and the payment system is secure."</p>
                    <div class="testimonial-author">
                        <img src="assets/images/placeholder.php?w=50&h=50&text=SJ" alt="Sarah Johnson">
                        <div class="author-info">
                            <h5>Sarah Johnson</h5>
                            <span>UI/UX Designer</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="testimonial-card">
                <div class="testimonial-content">
                    <div class="quote-icon">
                        <i class="fas fa-quote-left"></i>
                    </div>
                    <p>"As a startup founder, Lenze has been invaluable. I've hired talented developers and designers who helped bring my vision to life. Highly recommended!"</p>
                    <div class="testimonial-author">
                        <img src="assets/images/placeholder.php?w=50&h=50&text=MD" alt="Mark Davis">
                        <div class="author-info">
                            <h5>Mark Davis</h5>
                            <span>CEO, TechStart</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="testimonial-card">
                <div class="testimonial-content">
                    <div class="quote-icon">
                        <i class="fas fa-quote-left"></i>
                    </div>
                    <p>"The quality of freelancers on Lenze is exceptional. I've completed over 50 projects and the experience has been seamless every time."</p>
                    <div class="testimonial-author">
                        <img src="assets/images/placeholder.php?w=50&h=50&text=EC" alt="Emily Chen">
                        <div class="author-info">
                            <h5>Emily Chen</h5>
                            <span>Marketing Manager</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Top Freelancers Section -->
<section class="top-freelancers">
    <div class="container">
        <div class="section-header">
            <h2>Top Rated Freelancers</h2>
            <p>Work with the best talent in the industry</p>
        </div>
        
        <div class="freelancers-grid">
            <?php foreach($topFreelancers as $freelancer): ?>
            <div class="freelancer-card">
                <div class="freelancer-avatar">
                    <img src="assets/images/placeholder.php?w=100&h=100&text=<?php echo urlencode(substr($freelancer['name'], 0, 2)); ?>" alt="<?php echo $freelancer['name']; ?>">
                    <?php if($freelancer['is_pro']): ?>
                    <span class="badge">PRO</span>
                    <?php endif; ?>
                </div>
                <h5><?php echo $freelancer['name']; ?></h5>
                <p class="title"><?php echo $freelancer['title']; ?></p>
                <div class="rating">
                    <?php echo generateStarRating($freelancer['rating']); ?>
                    <span><?php echo $freelancer['rating']; ?> (<?php echo $freelancer['review_count']; ?>)</span>
                </div>
                <p class="rate">$<?php echo $freelancer['hourly_rate']; ?>/hr</p>
                <button class="btn btn-primary btn-sm">View Profile</button>
            </div>
            <?php endforeach; ?>
        </div>
        
        <div class="text-center mt-4">
            <a href="#" class="btn btn-outline">View All Freelancers</a>
        </div>
    </div>
</section>

<!-- Newsletter Section -->
<section class="newsletter">
    <div class="container">
        <div class="newsletter-wrapper">
            <div class="newsletter-content">
                <h2>Subscribe to Our Newsletter</h2>
                <p>Get the latest job opportunities and freelancing tips delivered to your inbox</p>
                
                <form class="newsletter-form" id="newsletterForm">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <div class="form-group">
                        <input type="email" name="email" class="form-control" placeholder="Enter your email address" required>
                        <button type="submit" class="btn btn-primary">Subscribe</button>
                    </div>
                    <div class="newsletter-message"></div>
                </form>
                
                <p class="newsletter-note">
                    <i class="fas fa-lock"></i> We respect your privacy. Unsubscribe at any time.
                </p>
            </div>
            
            <div class="newsletter-decoration">
                <div class="circle-bg"></div>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="faq" id="faq">
    <div class="container">
        <div class="section-header">
            <h2>Frequently Asked Questions</h2>
            <p>Find answers to common questions about our platform</p>
        </div>
        
        <div class="faq-grid">
            <div class="faq-column">
                <?php 
                $faqHalf = ceil(count($faqs) / 2);
                $faqsLeft = array_slice($faqs, 0, $faqHalf);
                $faqsRight = array_slice($faqs, $faqHalf);
                
                foreach($faqsLeft as $index => $faq): 
                ?>
                <div class="faq-item">
                    <div class="faq-question" data-faq="<?php echo $index; ?>">
                        <h4><?php echo $faq['question']; ?></h4>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer" id="faq-<?php echo $index; ?>">
                        <p><?php echo $faq['answer']; ?></p>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            
            <div class="faq-column">
                <?php foreach($faqsRight as $index => $faq): ?>
                <div class="faq-item">
                    <div class="faq-question" data-faq="<?php echo $index + $faqHalf; ?>">
                        <h4><?php echo $faq['question']; ?></h4>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer" id="faq-<?php echo $index + $faqHalf; ?>">
                        <p><?php echo $faq['answer']; ?></p>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</section>

<!-- Blog Section -->
<section class="blog">
    <div class="container">
        <div class="section-header">
            <h2>Our Latest Blog Posts</h2>
            <p>Stay updated with the latest trends and tips in freelancing</p>
        </div>
        
        <div class="blog-grid">
            <?php foreach($blogPosts as $index => $post): ?>
            <div class="blog-card">
                <div class="blog-image">
                    <img src="assets/images/placeholder.php?w=400&h=200&text=Blog+<?php echo ($index + 1); ?>" alt="<?php echo $post['title']; ?>">
                    <span class="blog-category"><?php echo $post['category']; ?></span>
                </div>
                <div class="blog-content">
                    <div class="blog-meta">
                        <span><i class="far fa-calendar"></i> <?php echo formatDate($post['date']); ?></span>
                        <span><i class="far fa-user"></i> <?php echo $post['author']; ?></span>
                    </div>
                    <h4><a href="#"><?php echo $post['title']; ?></a></h4>
                    <p><?php echo $post['excerpt']; ?></p>
                    <a href="#" class="read-more">Read More <i class="fas fa-arrow-right"></i></a>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- Bottom CTA Boxes -->
<section class="bottom-cta">
    <div class="container">
        <div class="cta-boxes-grid">
            <div class="cta-box-item">
                <div class="cta-box-content">
                    <h3>Ready to Get Started?</h3>
                    <p>Join thousands of freelancers and clients already using our platform</p>
                    <a href="#" class="btn btn-primary">Create Free Account</a>
                </div>
                <div class="cta-box-icon">
                    <i class="fas fa-rocket"></i>
                </div>
            </div>
            
            <div class="cta-box-item">
                <div class="cta-box-content">
                    <h3>Need Help?</h3>
                    <p>Our support team is here to help you 24/7 with any questions</p>
                    <a href="#" class="btn btn-white">Contact Support</a>
                </div>
                <div class="cta-box-icon">
                    <i class="fas fa-headset"></i>
                </div>
            </div>
        </div>
    </div>
</section>

<?php
// Include footer
include 'includes/footer.php';
?>