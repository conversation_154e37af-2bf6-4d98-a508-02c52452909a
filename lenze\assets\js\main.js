// Main JavaScript for Lenze Platform
$(document).ready(function() {
    
    // Mobile Menu Toggle
    const mobileToggle = $('#mobileToggle');
    const navMenu = $('#navMenu');
    const navAuth = $('.nav-auth');
    const mobileOverlay = $('#mobileOverlay');
    
    mobileToggle.click(function() {
        $(this).toggleClass('active');
        navMenu.toggleClass('active');
        navAuth.toggleClass('active');
        mobileOverlay.toggleClass('active');
        $('body').toggleClass('no-scroll');
    });
    
    // Close mobile menu when clicking overlay
    mobileOverlay.click(function() {
        mobileToggle.removeClass('active');
        navMenu.removeClass('active');
        navAuth.removeClass('active');
        mobileOverlay.removeClass('active');
        $('body').removeClass('no-scroll');
    });
    
    // Smooth scrolling for anchor links
    $('a[href^="#"]').click(function(e) {
        e.preventDefault();
        const target = $(this.hash);
        if (target.length) {
            // Close mobile menu if open
            if (navMenu.hasClass('active')) {
                mobileToggle.click();
            }
            
            $('html, body').animate({
                scrollTop: target.offset().top - 80
            }, 800);
        }
    });
    
    // Sticky navigation on scroll
    let lastScroll = 0;
    const navbar = $('.navbar');
    
    $(window).scroll(function() {
        const currentScroll = $(this).scrollTop();
        
        if (currentScroll > 100) {
            navbar.addClass('scrolled');
            
            // Hide/show navbar based on scroll direction
            if (currentScroll > lastScroll && currentScroll > 200) {
                navbar.addClass('hidden');
            } else {
                navbar.removeClass('hidden');
            }
        } else {
            navbar.removeClass('scrolled hidden');
        }
        
        lastScroll = currentScroll;
    });
    
    // FAQ Accordion
    $('.faq-question').click(function() {
        const faqItem = $(this).parent();
        const faqId = $(this).data('faq');
        const faqAnswer = $('#faq-' + faqId);
        
        // Close other FAQ items
        $('.faq-item').not(faqItem).removeClass('active');
        
        // Toggle current FAQ item
        faqItem.toggleClass('active');
    });
    
    // Newsletter Form Submission
    $('#newsletterForm').submit(function(e) {
        e.preventDefault();
        
        const form = $(this);
        const submitBtn = form.find('button[type="submit"]');
        const messageDiv = form.find('.newsletter-message');
        
        // Disable submit button
        submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Subscribing...');
        
        // Clear previous messages
        messageDiv.removeClass('success error').hide();
        
        // Submit form via AJAX
        $.ajax({
            url: 'forms/process-newsletter.php',
            type: 'POST',
            data: form.serialize(),
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    messageDiv.addClass('success').html(response.message).fadeIn();
                    form[0].reset();
                } else {
                    messageDiv.addClass('error').html(response.message).fadeIn();
                }
            },
            error: function() {
                messageDiv.addClass('error').html('An error occurred. Please try again.').fadeIn();
            },
            complete: function() {
                // Re-enable submit button
                submitBtn.prop('disabled', false).html('Subscribe');
            }
        });
    });
    
    // Search Form Enhancement
    $('.search-input').on('input', function() {
        const value = $(this).val();
        
        // You can implement autocomplete here
        // For now, just add a visual feedback
        if (value.length > 2) {
            $(this).addClass('has-value');
        } else {
            $(this).removeClass('has-value');
        }
    });
    
    // Category and Location Select Enhancement
    $('.search-select').change(function() {
        if ($(this).val()) {
            $(this).addClass('has-value');
        } else {
            $(this).removeClass('has-value');
        }
    });
    
    // Portfolio Hover Effect
    $('.portfolio-item').hover(
        function() {
            $(this).find('.portfolio-overlay').css('transform', 'translateY(0)');
        },
        function() {
            $(this).find('.portfolio-overlay').css('transform', 'translateY(100%)');
        }
    );
    
    // Lazy Loading for Images
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src || img.src;
                    img.classList.add('loaded');
                    observer.unobserve(img);
                }
            });
        });
        
        // Observe all images with data-src attribute
        $('img[data-src]').each(function() {
            imageObserver.observe(this);
        });
    }
    
    // Counter Animation for Statistics
    function animateCounters() {
        $('.stat-box h3').each(function() {
            const $this = $(this);
            const countTo = $this.text();
            
            // Extract number from text
            const match = countTo.match(/(\d+)/);
            if (match) {
                const num = parseInt(match[1]);
                const suffix = countTo.replace(/\d+/, '');
                
                $this.data('count', num);
                $this.data('suffix', suffix);
                $this.text('0' + suffix);
            }
        });
        
        // Animate on scroll
        const counterObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting && !$(entry.target).hasClass('counted')) {
                    const $counter = $(entry.target).find('h3');
                    const countTo = $counter.data('count');
                    const suffix = $counter.data('suffix');
                    
                    if (countTo) {
                        $({ countNum: 0 }).animate({
                            countNum: countTo
                        }, {
                            duration: 2000,
                            easing: 'swing',
                            step: function() {
                                $counter.text(Math.floor(this.countNum) + suffix);
                            },
                            complete: function() {
                                $counter.text(countTo + suffix);
                            }
                        });
                        
                        $(entry.target).addClass('counted');
                    }
                }
            });
        }, { threshold: 0.5 });
        
        $('.stat-box').each(function() {
            counterObserver.observe(this);
        });
    }
    
    animateCounters();
    
    // Form Focus Effects
    $('.form-control').focus(function() {
        $(this).parent().addClass('focused');
    }).blur(function() {
        if (!$(this).val()) {
            $(this).parent().removeClass('focused');
        }
    });
    
    // Add animation classes on scroll
    function animateOnScroll() {
        const animateElements = $('.animate-on-scroll');
        
        const animateObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    $(entry.target).addClass('animated');
                }
            });
        }, { threshold: 0.1 });
        
        animateElements.each(function() {
            animateObserver.observe(this);
        });
    }
    
    // Initialize animations
    animateOnScroll();
    
    // Freelancer Card Hover Effect
    $('.freelancer-card').hover(
        function() {
            $(this).find('img').css('transform', 'scale(1.1)');
        },
        function() {
            $(this).find('img').css('transform', 'scale(1)');
        }
    );
    
    // Blog Card Read More Animation
    $('.read-more').hover(
        function() {
            $(this).find('i').css('transform', 'translateX(5px)');
        },
        function() {
            $(this).find('i').css('transform', 'translateX(0)');
        }
    );
    
    // Initialize tooltips if needed
    $('[data-toggle="tooltip"]').tooltip();
    
    // Handle window resize
    let resizeTimer;
    $(window).resize(function() {
        clearTimeout(resizeTimer);
        resizeTimer = setTimeout(function() {
            // Reset mobile menu on desktop resize
            if ($(window).width() > 768) {
                mobileToggle.removeClass('active');
                navMenu.removeClass('active');
                navAuth.removeClass('active');
                mobileOverlay.removeClass('active');
                $('body').removeClass('no-scroll');
            }
        }, 250);
    });
    
    // Preloader (optional)
    $(window).on('load', function() {
        $('.preloader').fadeOut('slow');
    });
    
});

// Additional helper functions

// Debounce function for performance
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Format currency
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(amount);
}

// Get URL parameters
function getUrlParameter(name) {
    name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
    const regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
    const results = regex.exec(location.search);
    return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
}

// Set cookie
function setCookie(name, value, days) {
    const date = new Date();
    date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
    const expires = "expires=" + date.toUTCString();
    document.cookie = name + "=" + value + ";" + expires + ";path=/";
}

// Get cookie
function getCookie(name) {
    const nameEQ = name + "=";
    const ca = document.cookie.split(';');
    for(let i = 0; i < ca.length; i++) {
        let c = ca[i];
        while (c.charAt(0) == ' ') c = c.substring(1, c.length);
        if (c.indexOf(nameEQ) == 0) return c.substring(nameEQ.length, c.length);
    }
    return null;
}