<?php
// Include configuration and functions
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Set JSON header
header('Content-Type: application/json');

// Initialize response array
$response = [
    'success' => false,
    'message' => ''
];

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $response['message'] = 'Invalid request method.';
    echo json_encode($response);
    exit;
}

// Validate CSRF token
if (!isset($_POST['csrf_token']) || !validateCSRFToken($_POST['csrf_token'])) {
    $response['message'] = 'Invalid security token. Please refresh the page and try again.';
    echo json_encode($response);
    exit;
}

// Get and validate email
$email = isset($_POST['email']) ? sanitizeInput($_POST['email']) : '';

if (empty($email)) {
    $response['message'] = 'Email address is required.';
    echo json_encode($response);
    exit;
}

if (!validateEmail($email)) {
    $response['message'] = 'Please enter a valid email address.';
    echo json_encode($response);
    exit;
}

try {
    // Get database connection
    $conn = getDbConnection();
    
    // Check if email already exists
    $stmt = $conn->prepare("SELECT id FROM newsletter WHERE email = ?");
    $stmt->execute([$email]);
    
    if ($stmt->rowCount() > 0) {
        $response['success'] = false;
        $response['message'] = 'This email is already subscribed to our newsletter.';
    } else {
        // Insert new subscriber
        $stmt = $conn->prepare("INSERT INTO newsletter (email, subscribed_at) VALUES (?, NOW())");
        $stmt->execute([$email]);
        
        $response['success'] = true;
        $response['message'] = 'Thank you for subscribing! You\'ll receive our latest updates soon.';
        
        // Optionally send welcome email here
        // sendWelcomeEmail($email);
    }
    
} catch (PDOException $e) {
    // Log error (in production, don't expose database errors)
    error_log('Newsletter subscription error: ' . $e->getMessage());
    
    $response['success'] = false;
    $response['message'] = 'An error occurred while processing your subscription. Please try again later.';
}

// Return JSON response
echo json_encode($response);
?>