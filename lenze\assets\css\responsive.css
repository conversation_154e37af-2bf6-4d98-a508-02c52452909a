/* ===================================
   Lenze - Responsive CSS
   =================================== */

/* Large devices (desktops, 992px and below) */
@media (max-width: 992px) {
    /* Navigation adjustments */
    .nav-menu {
        gap: 20px;
    }
    
    /* Hero adjustments */
    .hero-wrapper {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .hero-content {
        order: 2;
    }
    
    .hero-image {
        order: 1;
        max-width: 400px;
        margin: 0 auto;
    }
    
    .hero-stats {
        justify-content: center;
    }
    
    /* Categories - 3 columns */
    .categories-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    /* Stats - 2 columns */
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    /* Features - 2 columns */
    .features-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    /* Portfolio - 2 columns */
    .portfolio-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    /* Freelancers - 3 columns */
    .freelancers-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* Medium devices (tablets, 768px and below) */
@media (max-width: 768px) {
    /* Section padding */
    section {
        padding: var(--section-padding-mobile);
    }
    
    /* Typography adjustments */
    h1 {
        font-size: 36px;
        line-height: 44px;
    }
    
    h2 {
        font-size: 28px;
        line-height: 36px;
    }
    
    h3 {
        font-size: 24px;
        line-height: 32px;
    }
    
    /* Navigation mobile menu */
    .mobile-toggle {
        display: flex;
    }
    
    .nav-menu {
        display: none;
        position: fixed;
        top: 70px;
        left: 0;
        right: 0;
        background-color: white;
        flex-direction: column;
        padding: 20px;
        box-shadow: 0 5px 20px var(--shadow);
        z-index: 998;
    }
    
    .nav-menu.active {
        display: flex;
    }
    
    .nav-auth {
        display: none;
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: white;
        padding: 20px;
        box-shadow: 0 -5px 20px var(--shadow);
        z-index: 998;
    }
    
    .nav-auth.active {
        display: flex;
        justify-content: center;
    }
    
    .mobile-overlay.active {
        display: block;
    }
    
    /* Hero search form */
    .search-inputs {
        flex-direction: column;
    }
    
    .search-select,
    .search-input,
    .btn-search {
        width: 100%;
    }
    
    /* Categories - 2 columns */
    .categories-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--grid-gap-medium);
    }
    
    /* Dual CTA - stack */
    .cta-grid {
        grid-template-columns: 1fr;
    }
    
    .cta-box {
        flex-direction: column;
        text-align: center;
        padding: var(--card-padding);
    }
    
    .cta-image {
        order: -1;
    }
    
    /* Process - stack */
    .process-grid {
        grid-template-columns: 1fr;
        gap: 40px;
    }
    
    /* Secondary hero - stack */
    .secondary-hero-wrapper {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .secondary-hero-image {
        max-width: 400px;
        margin: 0 auto;
    }
    
    /* Testimonials - stack */
    .testimonials-grid {
        grid-template-columns: 1fr;
    }
    
    /* Freelancers - 2 columns */
    .freelancers-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    /* Newsletter - stack */
    .newsletter-wrapper {
        grid-template-columns: 1fr;
    }
    
    .newsletter-decoration {
        display: none;
    }
    
    /* FAQ - stack */
    .faq-grid {
        grid-template-columns: 1fr;
    }
    
    /* Blog - stack */
    .blog-grid {
        grid-template-columns: 1fr;
    }
    
    /* Bottom CTA - stack */
    .cta-boxes-grid {
        grid-template-columns: 1fr;
    }
    
    /* Footer - adjust columns */
    .footer-content {
        grid-template-columns: 1fr 1fr;
        gap: 30px;
    }
    
    .footer-column:first-child {
        grid-column: 1 / -1;
    }
    
    .footer-bottom-content {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
}

/* Small devices (phones, 576px and below) */
@media (max-width: 576px) {
    /* Container padding */
    .container {
        padding: 0 10px;
    }
    
    /* Card padding */
    .category-card,
    .stat-box,
    .feature-card,
    .testimonial-card,
    .freelancer-card {
        padding: var(--card-padding-mobile);
    }
    
    /* Typography */
    h1 {
        font-size: 30px;
        line-height: 38px;
    }
    
    h2 {
        font-size: 24px;
        line-height: 32px;
    }
    
    .section-header p {
        font-size: 16px;
    }
    
    /* Buttons */
    .btn {
        padding: 10px 24px;
        font-size: 14px;
    }
    
    /* Hero adjustments */
    .hero {
        padding-top: 100px;
    }
    
    .hero-subtitle {
        font-size: 16px;
    }
    
    .hero-stats {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }
    
    /* Categories - single column */
    .categories-grid {
        grid-template-columns: 1fr;
    }
    
    /* Stats - single column */
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    /* Features - single column */
    .features-grid {
        grid-template-columns: 1fr;
    }
    
    /* Portfolio - single column */
    .portfolio-grid {
        grid-template-columns: 1fr;
    }
    
    /* Freelancers - single column */
    .freelancers-grid {
        grid-template-columns: 1fr;
    }
    
    /* Newsletter form */
    .newsletter-form .form-group {
        flex-direction: column;
    }
    
    .form-control {
        width: 100%;
    }
    
    /* Footer - single column */
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .footer-social {
        justify-content: center;
    }
    
    .footer-bottom-links {
        flex-wrap: wrap;
        justify-content: center;
        gap: 10px;
    }
    
    /* CTA boxes */
    .cta-box-item {
        flex-direction: column;
        text-align: center;
        padding: 30px 20px;
    }
    
    .cta-box-icon {
        font-size: 40px;
        margin-bottom: 20px;
    }
}

/* Extra small devices (phones, 400px and below) */
@media (max-width: 400px) {
    /* Logo size */
    .nav-brand .logo {
        font-size: 24px;
    }
    
    /* Search form adjustments */
    .search-inputs {
        padding: 8px;
    }
    
    .search-select,
    .search-input {
        font-size: 13px;
        padding: 10px 12px;
    }
    
    /* Icon sizes */
    .category-icon,
    .stat-icon,
    .feature-icon {
        width: 60px;
        height: 60px;
    }
    
    .category-icon i,
    .feature-icon i {
        font-size: 28px;
    }
    
    .stat-icon i {
        font-size: 24px;
    }
    
    /* Process step adjustments */
    .process-step {
        flex-direction: column;
        text-align: center;
    }
    
    .step-number {
        margin-bottom: 10px;
    }
}

/* Print styles */
@media print {
    /* Hide navigation and footer */
    .navbar,
    .footer,
    .mobile-overlay,
    .btn,
    .search-form,
    .newsletter-form {
        display: none !important;
    }
    
    /* Adjust sections for print */
    section {
        page-break-inside: avoid;
        padding: 20px 0;
    }
    
    /* Show all content */
    .faq-answer {
        max-height: none !important;
        padding: 0 25px 20px !important;
    }
    
    /* Remove backgrounds */
    .hero,
    .secondary-hero,
    .testimonials,
    .cta-box-item {
        background-color: white !important;
        color: var(--text-primary) !important;
    }
    
    .hero h1,
    .hero p,
    .secondary-hero h2,
    .secondary-hero p,
    .testimonials h2,
    .testimonials p,
    .cta-box-content h3,
    .cta-box-content p {
        color: var(--text-primary) !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .btn-primary {
        border: 2px solid var(--primary-green);
    }
    
    .btn-outline {
        border-width: 3px;
    }
    
    .form-control {
        border-width: 2px;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Dark mode support (optional - can be toggled via JavaScript) */
@media (prefers-color-scheme: dark) {
    body.dark-mode {
        background-color: var(--bg-dark);
        color: white;
    }
    
    body.dark-mode .navbar,
    body.dark-mode .category-card,
    body.dark-mode .feature-card,
    body.dark-mode .freelancer-card,
    body.dark-mode .blog-card,
    body.dark-mode .faq-item {
        background-color: var(--bg-footer);
        color: white;
    }
    
    body.dark-mode h1,
    body.dark-mode h2,
    body.dark-mode h3,
    body.dark-mode h4,
    body.dark-mode h5,
    body.dark-mode h6 {
        color: white;
    }
    
    body.dark-mode .nav-link,
    body.dark-mode .blog-content h4 a {
        color: white;
    }
    
    body.dark-mode .nav-link:hover,
    body.dark-mode .nav-link.active,
    body.dark-mode .blog-content h4 a:hover {
        color: var(--primary-green);
    }
    
    body.dark-mode .btn-outline {
        border-color: white;
        color: white;
    }
    
    body.dark-mode .btn-outline:hover {
        border-color: var(--primary-green);
        color: var(--primary-green);
    }
    
    body.dark-mode .form-control {
        background-color: var(--bg-footer);
        border-color: rgba(255, 255, 255, 0.2);
        color: white;
    }
    
    body.dark-mode .search-select,
    body.dark-mode .search-input {
        background-color: var(--bg-footer);
        color: white;
    }
}