<?php
// Include configuration and functions
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Get search parameters
$location = isset($_GET['location']) ? sanitizeInput($_GET['location']) : '';
$category = isset($_GET['category']) ? sanitizeInput($_GET['category']) : '';
$keyword = isset($_GET['keyword']) ? sanitizeInput($_GET['keyword']) : '';

// Build search query parameters
$searchParams = [];
if (!empty($location)) {
    $searchParams['location'] = $location;
}
if (!empty($category)) {
    $searchParams['category'] = $category;
}
if (!empty($keyword)) {
    $searchParams['keyword'] = $keyword;
}

// For now, redirect to a search results page (to be implemented)
// In a real application, this would query the database and display results
$queryString = http_build_query($searchParams);
$redirectUrl = SITE_URL . 'search-results.php?' . $queryString;

// If no search parameters provided, redirect back to home
if (empty($searchParams)) {
    header('Location: ' . SITE_URL);
    exit;
}

// Store search in session for analytics (optional)
if (!isset($_SESSION['search_history'])) {
    $_SESSION['search_history'] = [];
}

$_SESSION['search_history'][] = [
    'params' => $searchParams,
    'timestamp' => time()
];

// Limit search history to last 10 searches
if (count($_SESSION['search_history']) > 10) {
    array_shift($_SESSION['search_history']);
}

// In a real implementation, you would:
// 1. Query the database for matching jobs/freelancers
// 2. Display results on a search results page
// 3. Implement pagination for large result sets
// 4. Add filters and sorting options

// For now, we'll create a simple message and redirect
$_SESSION['search_message'] = 'Search results for: ' . 
    (!empty($keyword) ? '"' . $keyword . '" ' : '') .
    (!empty($category) ? 'in ' . getCategoryName($category) . ' ' : '') .
    (!empty($location) ? 'at ' . getLocationName($location) : '');

// Redirect to search results page (or back to home with message)
header('Location: ' . SITE_URL . '?search=1#results');
exit;

// Helper function to get category name (would query database in real app)
function getCategoryName($categoryId) {
    $categories = getCategories();
    foreach ($categories as $cat) {
        if ($cat['id'] == $categoryId) {
            return $cat['name'];
        }
    }
    return 'All Categories';
}

// Helper function to get location name (would query database in real app)
function getLocationName($locationId) {
    $locations = getLocations();
    foreach ($locations as $loc) {
        if ($loc['id'] == $locationId) {
            return $loc['city'] . ', ' . $loc['country'];
        }
    }
    return 'All Locations';
}
?>