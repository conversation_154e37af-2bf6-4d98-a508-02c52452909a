# Lenze Freelance Platform

A modern freelance platform landing page built with PHP, featuring a responsive design and interactive elements.

## Features

- ✅ Fully responsive design
- ✅ Modern green color scheme (#00C853)
- ✅ Hero section with search functionality
- ✅ Job categories grid
- ✅ Statistics section
- ✅ Dual CTA sections for freelancers and clients
- ✅ Features showcase
- ✅ Portfolio gallery
- ✅ User testimonials
- ✅ Top freelancers section
- ✅ Newsletter signup
- ✅ FAQ accordion
- ✅ Blog section
- ✅ Contact form handling
- ✅ API endpoints

## Setup Instructions

### Prerequisites

- XAMPP installed with Apache and MySQL running
- PHP 7.4 or higher
- MySQL 5.7 or higher

### Installation

1. **Place files in XAMPP htdocs**
   - The project should be in: `C:\xampp\htdocs\lenze\`

2. **Setup the database**
   - Open terminal/command prompt
   - Navigate to the project directory: `cd C:\xampp\htdocs\lenze`
   - Run: `php setup-database.php`
   - This will create the database and tables with sample data

3. **Access the site**
   - Open your browser
   - Navigate to: `http://localhost/lenze/`

## Project Structure

```
lenze/
├── index.php                 # Main landing page
├── setup-database.php        # Database setup script
├── README.md                 # This file
├── includes/
│   ├── config.php           # Configuration file
│   ├── header.php           # Header template
│   ├── footer.php           # Footer template
│   ├── functions.php        # Helper functions
│   └── components/          # Page components
├── assets/
│   ├── css/
│   │   ├── style.css        # Main styles
│   │   └── responsive.css   # Responsive styles
│   ├── js/
│   │   ├── main.js          # Main JavaScript
│   │   └── form-validation.js # Form validation
│   ├── images/
│   │   └── placeholder.php  # Dynamic placeholder generator
│   ├── icons/
│   └── fonts/
├── forms/
│   ├── process-newsletter.php
│   ├── search-handler.php
│   └── process-contact.php
└── api/
    ├── get-categories.php
    ├── get-locations.php
    └── get-freelancers.php
```

## API Endpoints

- **GET** `/api/get-categories.php` - Get all job categories
- **GET** `/api/get-locations.php` - Get all available locations
- **GET** `/api/get-freelancers.php` - Get freelancers with pagination

### API Parameters for get-freelancers.php:
- `page` - Page number (default: 1)
- `limit` - Items per page (default: 10, max: 50)
- `category` - Filter by category ID
- `location` - Filter by location ID
- `min_rating` - Minimum rating filter
- `sort` - Sort order: rating_desc, rating_asc, rate_desc, rate_asc

## Form Handlers

### Newsletter Signup
- **POST** `/forms/process-newsletter.php`
- Required: `email`, `csrf_token`
- Returns JSON response

### Search
- **GET** `/forms/search-handler.php`
- Optional: `location`, `category`, `keyword`

### Contact Form
- **POST** `/forms/process-contact.php`
- Required: `name`, `email`, `subject`, `message`, `csrf_token`
- Optional: `phone`
- Returns JSON response

## Placeholder Images

The site uses a dynamic placeholder image generator. Access it via:
```
/assets/images/placeholder.php?w=400&h=300&text=Your+Text&bg=00C853&color=FFFFFF
```

Parameters:
- `w` - Width (default: 400)
- `h` - Height (default: 300)
- `text` - Display text
- `bg` - Background color hex (without #)
- `color` - Text color hex (without #)

## Technologies Used

- **Backend**: PHP 7.4+
- **Database**: MySQL
- **Frontend**: HTML5, CSS3, JavaScript
- **Libraries**: jQuery, Font Awesome
- **Font**: Google Fonts (Poppins)

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Mobile browsers

## Security Features

- CSRF protection for forms
- Input sanitization
- Prepared statements for database queries
- XSS protection

## Performance Optimizations

- Lazy loading for images
- Minification ready
- Responsive images
- Optimized database queries

## Future Enhancements

- User authentication system
- Advanced search filters
- Real-time messaging
- Payment integration
- Admin dashboard
- Email notifications

## License

This project is created for demonstration purposes.

## Support

For any issues or questions, <NAME_EMAIL>