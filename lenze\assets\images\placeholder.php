<?php
// Placeholder image generator
header('Content-Type: image/svg+xml');

// Get parameters
$width = isset($_GET['w']) ? intval($_GET['w']) : 400;
$height = isset($_GET['h']) ? intval($_GET['h']) : 300;
$text = isset($_GET['text']) ? htmlspecialchars($_GET['text']) : "{$width}x{$height}";
$bg = isset($_GET['bg']) ? '#' . preg_replace('/[^0-9a-fA-F]/', '', $_GET['bg']) : '#00C853';
$color = isset($_GET['color']) ? '#' . preg_replace('/[^0-9a-fA-F]/', '', $_GET['color']) : '#FFFFFF';

// Generate SVG
?>
<svg width="<?php echo $width; ?>" height="<?php echo $height; ?>" xmlns="http://www.w3.org/2000/svg">
    <rect width="100%" height="100%" fill="<?php echo $bg; ?>"/>
    <text x="50%" y="50%" font-family="Poppins, Arial, sans-serif" font-size="20" fill="<?php echo $color; ?>" text-anchor="middle" dominant-baseline="middle">
        <?php echo $text; ?>
    </text>
</svg>