<?php
// Include configuration and functions
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Set JSON header
header('Content-Type: application/json');

// Initialize response array
$response = [
    'success' => false,
    'message' => '',
    'errors' => []
];

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $response['message'] = 'Invalid request method.';
    echo json_encode($response);
    exit;
}

// Validate CSRF token
if (!isset($_POST['csrf_token']) || !validateCSRFToken($_POST['csrf_token'])) {
    $response['message'] = 'Invalid security token. Please refresh the page and try again.';
    echo json_encode($response);
    exit;
}

// Get form data
$name = isset($_POST['name']) ? sanitizeInput($_POST['name']) : '';
$email = isset($_POST['email']) ? sanitizeInput($_POST['email']) : '';
$subject = isset($_POST['subject']) ? sanitizeInput($_POST['subject']) : '';
$message = isset($_POST['message']) ? sanitizeInput($_POST['message']) : '';
$phone = isset($_POST['phone']) ? sanitizeInput($_POST['phone']) : '';

// Validate required fields
$errors = [];

if (empty($name)) {
    $errors['name'] = 'Name is required.';
}

if (empty($email)) {
    $errors['email'] = 'Email address is required.';
} elseif (!validateEmail($email)) {
    $errors['email'] = 'Please enter a valid email address.';
}

if (empty($subject)) {
    $errors['subject'] = 'Subject is required.';
}

if (empty($message)) {
    $errors['message'] = 'Message is required.';
} elseif (strlen($message) < 10) {
    $errors['message'] = 'Message must be at least 10 characters long.';
}

// Optional phone validation
if (!empty($phone) && !preg_match('/^[\+]?[(]?[0-9]{3}[)]?[-\s\.]?[(]?[0-9]{3}[)]?[-\s\.]?[0-9]{4,6}$/', $phone)) {
    $errors['phone'] = 'Please enter a valid phone number.';
}

// Check for errors
if (!empty($errors)) {
    $response['message'] = 'Please correct the errors below.';
    $response['errors'] = $errors;
    echo json_encode($response);
    exit;
}

try {
    // In a real application, you would:
    // 1. Save the contact message to database
    // 2. Send email notification to admin
    // 3. Send confirmation email to user
    
    // Get database connection
    $conn = getDbConnection();
    
    // Save contact message
    $stmt = $conn->prepare("
        INSERT INTO contact_messages (name, email, phone, subject, message, created_at) 
        VALUES (?, ?, ?, ?, ?, NOW())
    ");
    
    $stmt->execute([$name, $email, $phone, $subject, $message]);
    
    // Send email to admin (in production)
    $adminEmail = SITE_EMAIL;
    $emailSubject = "New Contact Message: " . $subject;
    $emailBody = "
        New contact message received:\n\n
        Name: $name\n
        Email: $email\n
        Phone: $phone\n
        Subject: $subject\n\n
        Message:\n$message
    ";
    
    // mail($adminEmail, $emailSubject, $emailBody, "From: $email");
    
    // Send confirmation to user
    $userSubject = "Thank you for contacting Lenze";
    $userBody = "
        Dear $name,\n\n
        Thank you for contacting us. We have received your message and will respond within 24-48 hours.\n\n
        Your message:\n
        Subject: $subject\n
        $message\n\n
        Best regards,\n
        The Lenze Team
    ";
    
    // mail($email, $userSubject, $userBody, "From: $adminEmail");
    
    $response['success'] = true;
    $response['message'] = 'Thank you for your message! We\'ll get back to you within 24-48 hours.';
    $response['reset'] = true;
    
} catch (PDOException $e) {
    // Log error
    error_log('Contact form error: ' . $e->getMessage());
    
    $response['success'] = false;
    $response['message'] = 'An error occurred while sending your message. Please try again later.';
}

// Return JSON response
echo json_encode($response);
?>