// Form Validation JavaScript for Lenze Platform
$(document).ready(function() {
    
    // Email validation regex
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    
    // Phone validation regex (international format)
    const phoneRegex = /^[\+]?[(]?[0-9]{3}[)]?[-\s\.]?[(]?[0-9]{3}[)]?[-\s\.]?[0-9]{4,6}$/;
    
    // URL validation regex
    const urlRegex = /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/;
    
    // Real-time validation for email inputs
    $('input[type="email"]').on('blur', function() {
        validateEmail($(this));
    });
    
    // Real-time validation for required fields
    $('input[required], textarea[required], select[required]').on('blur', function() {
        validateRequired($(this));
    });
    
    // Validate email
    function validateEmail(input) {
        const value = input.val().trim();
        const parent = input.parent();
        
        removeError(input);
        
        if (!value) {
            if (input.attr('required')) {
                showError(input, 'Email address is required');
                return false;
            }
            return true;
        }
        
        if (!emailRegex.test(value)) {
            showError(input, 'Please enter a valid email address');
            return false;
        }
        
        showSuccess(input);
        return true;
    }
    
    // Validate required fields
    function validateRequired(input) {
        const value = input.val().trim();
        
        removeError(input);
        
        if (!value) {
            showError(input, 'This field is required');
            return false;
        }
        
        showSuccess(input);
        return true;
    }
    
    // Validate phone number
    function validatePhone(input) {
        const value = input.val().trim();
        
        removeError(input);
        
        if (!value && !input.attr('required')) {
            return true;
        }
        
        if (!phoneRegex.test(value)) {
            showError(input, 'Please enter a valid phone number');
            return false;
        }
        
        showSuccess(input);
        return true;
    }
    
    // Validate URL
    function validateURL(input) {
        const value = input.val().trim();
        
        removeError(input);
        
        if (!value && !input.attr('required')) {
            return true;
        }
        
        if (!urlRegex.test(value)) {
            showError(input, 'Please enter a valid URL');
            return false;
        }
        
        showSuccess(input);
        return true;
    }
    
    // Validate password
    function validatePassword(input) {
        const value = input.val();
        const minLength = input.data('min-length') || 8;
        
        removeError(input);
        
        if (!value) {
            if (input.attr('required')) {
                showError(input, 'Password is required');
                return false;
            }
            return true;
        }
        
        if (value.length < minLength) {
            showError(input, `Password must be at least ${minLength} characters long`);
            return false;
        }
        
        // Check for password strength (optional)
        const hasUpperCase = /[A-Z]/.test(value);
        const hasLowerCase = /[a-z]/.test(value);
        const hasNumbers = /\d/.test(value);
        const hasSpecialChar = /[!@#$%^&*]/.test(value);
        
        if (input.data('strong-password')) {
            if (!hasUpperCase || !hasLowerCase || !hasNumbers || !hasSpecialChar) {
                showError(input, 'Password must contain uppercase, lowercase, numbers, and special characters');
                return false;
            }
        }
        
        showSuccess(input);
        return true;
    }
    
    // Validate password confirmation
    function validatePasswordConfirm(input, passwordInput) {
        const value = input.val();
        const passwordValue = passwordInput.val();
        
        removeError(input);
        
        if (!value) {
            if (input.attr('required')) {
                showError(input, 'Please confirm your password');
                return false;
            }
            return true;
        }
        
        if (value !== passwordValue) {
            showError(input, 'Passwords do not match');
            return false;
        }
        
        showSuccess(input);
        return true;
    }
    
    // Validate select dropdown
    function validateSelect(select) {
        const value = select.val();
        
        removeError(select);
        
        if (!value || value === '') {
            if (select.attr('required')) {
                showError(select, 'Please select an option');
                return false;
            }
        }
        
        showSuccess(select);
        return true;
    }
    
    // Validate checkbox
    function validateCheckbox(checkbox) {
        removeError(checkbox);
        
        if (checkbox.attr('required') && !checkbox.is(':checked')) {
            showError(checkbox, 'This field is required');
            return false;
        }
        
        return true;
    }
    
    // Validate number input
    function validateNumber(input) {
        const value = input.val();
        const min = parseFloat(input.attr('min'));
        const max = parseFloat(input.attr('max'));
        
        removeError(input);
        
        if (!value && input.attr('required')) {
            showError(input, 'This field is required');
            return false;
        }
        
        if (value && isNaN(value)) {
            showError(input, 'Please enter a valid number');
            return false;
        }
        
        if (min !== undefined && value < min) {
            showError(input, `Value must be at least ${min}`);
            return false;
        }
        
        if (max !== undefined && value > max) {
            showError(input, `Value must not exceed ${max}`);
            return false;
        }
        
        showSuccess(input);
        return true;
    }
    
    // Show error message
    function showError(input, message) {
        const parent = input.parent();
        input.addClass('is-invalid');
        
        // Remove any existing error message
        parent.find('.error-message').remove();
        
        // Add new error message
        $('<div class="error-message">' + message + '</div>').insertAfter(input);
    }
    
    // Show success state
    function showSuccess(input) {
        input.addClass('is-valid');
    }
    
    // Remove error state
    function removeError(input) {
        const parent = input.parent();
        input.removeClass('is-invalid is-valid');
        parent.find('.error-message').remove();
    }
    
    // Main form validation function
    function validateForm(form) {
        let isValid = true;
        
        // Validate all required fields
        form.find('input[required], textarea[required], select[required]').each(function() {
            const input = $(this);
            
            if (input.attr('type') === 'email') {
                if (!validateEmail(input)) isValid = false;
            } else if (input.attr('type') === 'tel') {
                if (!validatePhone(input)) isValid = false;
            } else if (input.attr('type') === 'url') {
                if (!validateURL(input)) isValid = false;
            } else if (input.attr('type') === 'password') {
                if (!validatePassword(input)) isValid = false;
            } else if (input.attr('type') === 'number') {
                if (!validateNumber(input)) isValid = false;
            } else if (input.is('select')) {
                if (!validateSelect(input)) isValid = false;
            } else if (input.attr('type') === 'checkbox') {
                if (!validateCheckbox(input)) isValid = false;
            } else {
                if (!validateRequired(input)) isValid = false;
            }
        });
        
        // Validate password confirmation if exists
        const passwordConfirm = form.find('input[name="password_confirm"]');
        if (passwordConfirm.length) {
            const password = form.find('input[name="password"]');
            if (!validatePasswordConfirm(passwordConfirm, password)) isValid = false;
        }
        
        return isValid;
    }
    
    // Attach validation to all forms with class 'needs-validation'
    $('.needs-validation').on('submit', function(e) {
        e.preventDefault();
        
        const form = $(this);
        
        if (validateForm(form)) {
            // Form is valid, submit it
            // You can add AJAX submission here or allow normal submission
            
            // For AJAX submission:
            submitFormAjax(form);
            
            // For normal submission, uncomment:
            // form[0].submit();
        } else {
            // Scroll to first error
            const firstError = form.find('.is-invalid').first();
            if (firstError.length) {
                $('html, body').animate({
                    scrollTop: firstError.offset().top - 100
                }, 500);
            }
        }
    });
    
    // AJAX form submission
    function submitFormAjax(form) {
        const submitBtn = form.find('button[type="submit"]');
        const originalText = submitBtn.html();
        
        // Disable submit button
        submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Processing...');
        
        $.ajax({
            url: form.attr('action'),
            type: form.attr('method') || 'POST',
            data: form.serialize(),
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    // Show success message
                    showFormMessage(form, 'success', response.message || 'Form submitted successfully!');
                    
                    // Reset form if needed
                    if (response.reset) {
                        form[0].reset();
                        form.find('.is-valid').removeClass('is-valid');
                    }
                    
                    // Redirect if URL provided
                    if (response.redirect) {
                        setTimeout(function() {
                            window.location.href = response.redirect;
                        }, 2000);
                    }
                } else {
                    // Show error message
                    showFormMessage(form, 'error', response.message || 'An error occurred. Please try again.');
                    
                    // Show field-specific errors if provided
                    if (response.errors) {
                        $.each(response.errors, function(field, message) {
                            const input = form.find('[name="' + field + '"]');
                            if (input.length) {
                                showError(input, message);
                            }
                        });
                    }
                }
            },
            error: function(xhr, status, error) {
                showFormMessage(form, 'error', 'An error occurred. Please try again.');
                console.error('Form submission error:', error);
            },
            complete: function() {
                // Re-enable submit button
                submitBtn.prop('disabled', false).html(originalText);
            }
        });
    }
    
    // Show form message
    function showFormMessage(form, type, message) {
        // Remove existing messages
        form.find('.form-message').remove();
        
        // Create message element
        const messageHtml = '<div class="form-message ' + type + '">' +
                          '<i class="fas fa-' + (type === 'success' ? 'check-circle' : 'exclamation-circle') + '"></i> ' +
                          message + '</div>';
        
        // Insert message before submit button
        form.find('button[type="submit"]').before(messageHtml);
        
        // Auto-hide success messages after 5 seconds
        if (type === 'success') {
            setTimeout(function() {
                form.find('.form-message').fadeOut();
            }, 5000);
        }
    }
    
    // Character counter for textareas
    $('textarea[data-max-length]').each(function() {
        const textarea = $(this);
        const maxLength = textarea.data('max-length');
        
        // Add counter element
        $('<div class="char-counter"><span class="current">0</span>/' + maxLength + '</div>').insertAfter(textarea);
        
        // Update counter on input
        textarea.on('input', function() {
            const length = $(this).val().length;
            const counter = $(this).next('.char-counter');
            counter.find('.current').text(length);
            
            if (length > maxLength) {
                counter.addClass('exceeded');
            } else {
                counter.removeClass('exceeded');
            }
        });
    });
    
    // File upload validation
    $('input[type="file"]').on('change', function() {
        const input = $(this);
        const files = this.files;
        const maxSize = input.data('max-size') || 5242880; // 5MB default
        const allowedTypes = input.data('allowed-types') ? input.data('allowed-types').split(',') : [];
        
        removeError(input);
        
        if (files.length > 0) {
            const file = files[0];
            
            // Check file size
            if (file.size > maxSize) {
                showError(input, 'File size must not exceed ' + formatFileSize(maxSize));
                input.val('');
                return;
            }
            
            // Check file type
            if (allowedTypes.length > 0) {
                const fileType = file.type;
                const fileExtension = file.name.split('.').pop().toLowerCase();
                
                let isAllowed = false;
                for (let type of allowedTypes) {
                    if (fileType.includes(type) || fileExtension === type) {
                        isAllowed = true;
                        break;
                    }
                }
                
                if (!isAllowed) {
                    showError(input, 'File type not allowed. Allowed types: ' + allowedTypes.join(', '));
                    input.val('');
                    return;
                }
            }
            
            showSuccess(input);
        }
    });
    
    // Format file size
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    // Password strength indicator
    $('input[type="password"][data-show-strength]').on('input', function() {
        const password = $(this).val();
        const strengthBar = $(this).siblings('.password-strength');
        
        if (!strengthBar.length) {
            $('<div class="password-strength"><div class="strength-bar"></div><span class="strength-text"></span></div>').insertAfter($(this));
        }
        
        const strength = calculatePasswordStrength(password);
        updatePasswordStrength($(this).siblings('.password-strength'), strength);
    });
    
    // Calculate password strength
    function calculatePasswordStrength(password) {
        let strength = 0;
        
        if (password.length >= 8) strength += 20;
        if (password.length >= 12) strength += 20;
        if (/[a-z]/.test(password)) strength += 20;
        if (/[A-Z]/.test(password)) strength += 20;
        if (/[0-9]/.test(password)) strength += 10;
        if (/[^a-zA-Z0-9]/.test(password)) strength += 10;
        
        return strength;
    }
    
    // Update password strength indicator
    function updatePasswordStrength(indicator, strength) {
        const bar = indicator.find('.strength-bar');
        const text = indicator.find('.strength-text');
        
        bar.css('width', strength + '%');
        
        if (strength < 40) {
            bar.css('background-color', '#e74c3c');
            text.text('Weak');
        } else if (strength < 70) {
            bar.css('background-color', '#f39c12');
            text.text('Medium');
        } else {
            bar.css('background-color', '#27ae60');
            text.text('Strong');
        }
    }
    
});

// CSS for validation states (add to your CSS file)
const validationStyles = `
<style>
.form-control.is-invalid {
    border-color: #e74c3c;
}

.form-control.is-valid {
    border-color: #27ae60;
}

.error-message {
    color: #e74c3c;
    font-size: 14px;
    margin-top: 5px;
}

.form-message {
    padding: 15px;
    margin: 20px 0;
    border-radius: 5px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.form-message.success {
    background-color: rgba(39, 174, 96, 0.1);
    color: #27ae60;
    border: 1px solid rgba(39, 174, 96, 0.3);
}

.form-message.error {
    background-color: rgba(231, 76, 60, 0.1);
    color: #e74c3c;
    border: 1px solid rgba(231, 76, 60, 0.3);
}

.char-counter {
    text-align: right;
    font-size: 12px;
    color: #7f8c8d;
    margin-top: 5px;
}

.char-counter.exceeded {
    color: #e74c3c;
}

.password-strength {
    margin-top: 5px;
    height: 4px;
    background-color: #ecf0f1;
    border-radius: 2px;
    overflow: hidden;
}

.strength-bar {
    height: 100%;
    width: 0;
    transition: width 0.3s, background-color 0.3s;
}

.strength-text {
    font-size: 12px;
    margin-left: 5px;
}
</style>
`;